// src/components/home/<USER>
"use client";

import { useRef, ReactNode, useMemo } from "react";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { useReducedMotion } from "framer-motion";

// Animation constants - extracted for maintainability and consistency
const ANIMATION_CONSTANTS = {
    /** Default intersection threshold for triggering animations */
    THRESHOLD: 0.2,
    /** Animation duration in milliseconds */
    DURATION: 700,
    /** Initial opacity for hidden state (0.1 instead of 0 for better accessibility) */
    INITIAL_OPACITY: 0.1,
    /** Translation distance in pixels for the slide-up effect */
    TRANSLATE_Y: 12,
    /** CSS transition timing function for smooth animations */
    EASING: "ease-out",
} as const;

/**
 * Props interface for the AnimatedSection component
 */
interface AnimatedSectionProps {
    /** Content to be animated */
    children: ReactNode;
    /** Additional CSS classes to apply */
    className?: string;
    /**
     * Custom intersection threshold (0-1)
     * @default 0.2
     */
    threshold?: number;
    /**
     * Whether to trigger animation only once
     * @default undefined (uses mobile detection)
     */
    triggerOnce?: boolean;
}

/**
 * AnimatedSection Component
 *
 * A reusable component that provides smooth entrance animations for content
 * when it enters the viewport. Respects user's motion preferences and provides
 * different behavior for mobile vs desktop devices.
 *
 * Features:
 * - Intersection Observer API for efficient viewport detection
 * - Respects prefers-reduced-motion accessibility setting
 * - Mobile-optimized behavior (animate once vs re-animate on scroll)
 * - Customizable animation threshold
 * - Performance optimized with memoization
 *
 * @param props - Component props
 * @returns JSX element with animation capabilities
 */
const AnimatedSection = ({
    children,
    className,
    threshold = ANIMATION_CONSTANTS.THRESHOLD,
    triggerOnce,
}: AnimatedSectionProps): JSX.Element => {
    // Refs for DOM manipulation and intersection observation
    const sectionRef = useRef<HTMLDivElement>(null);

    // Motion and device detection hooks
    const prefersReducedMotion = useReducedMotion();
    const isMobile = useIsMobile();

    // Memoized intersection observer options for performance
    // Prevents unnecessary re-creation of options object on each render
    const intersectionOptions = useMemo(
        () => ({
            // Use explicit triggerOnce prop, fallback to mobile detection
            // Mobile: animate once for better performance and UX
            // Desktop: re-animate on scroll for engaging experience
            triggerOnce: triggerOnce !== undefined ? triggerOnce : isMobile,
            threshold,
        }),
        [triggerOnce, isMobile, threshold],
    );

    // Observe element visibility with memoized options
    const isVisible = useIntersectionObserver(sectionRef, intersectionOptions);

    // Memoized animation classes for performance optimization
    // Prevents unnecessary class string recalculation on each render
    const animationClasses = useMemo(() => {
        // Base transition classes - always applied for smooth animations
        const baseClasses = [
            `transition-all duration-${ANIMATION_CONSTANTS.DURATION} ${ANIMATION_CONSTANTS.EASING}`,
            "will-change-transform", // Optimized for transform animations
        ];

        // Conditional classes based on visibility and motion preferences
        const conditionalClasses = (() => {
            // If user prefers reduced motion, skip animations entirely
            if (prefersReducedMotion) {
                return ["opacity-100"]; // Always visible, no transforms
            }

            // Apply entrance animation based on visibility
            return isVisible
                ? ["opacity-100", "translate-y-0"] // Visible state: full opacity, no transform
                : [
                      `opacity-${ANIMATION_CONSTANTS.INITIAL_OPACITY * 10}`, // Hidden state: low opacity
                      `translate-y-${ANIMATION_CONSTANTS.TRANSLATE_Y}`, // Hidden state: translated down
                  ];
        })();

        return [...baseClasses, ...conditionalClasses];
    }, [isVisible, prefersReducedMotion]);

    return (
        <div
            ref={sectionRef}
            className={cn(...animationClasses, className)}
            // Accessibility: Ensure content is not hidden from screen readers
            aria-hidden={false}
            // Performance hint for browser optimization
            style={{
                // Only set will-change when animation is active to avoid performance issues
                willChange: isVisible ? "auto" : "transform, opacity",
            }}
        >
            {children}
        </div>
    );
};

export default AnimatedSection;
